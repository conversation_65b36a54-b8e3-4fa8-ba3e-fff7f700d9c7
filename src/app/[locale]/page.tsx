"use client"

import { HeroSection } from "@/components/blocks/hero-section"
import {Navbar1Demo} from "@/components/sections/header"
import { Icons } from "@/components/ui/icons"

export default function LocaleHomePage() {
  return (
    <>

    <Navbar1Demo />
    

    <HeroSection
      badge={{
        text: "Introducing our new components",
        action: {
          text: "Learn more",
          href: "/docs",
        },
      }}
      title="Build faster with beautiful components"
      description="Premium UI components built with React and Tailwind CSS. Save time and ship your next project faster with our ready-to-use components."
      actions={[
        {
          text: "Get Started",
          href: "/docs/getting-started",
          variant: "default",
        },
        {
          text: "GitHub",
          href: "https://github.com/your-repo",
          variant: "link",
          icon: <Icons.gitHub className="h-5 w-5" />,
        },
      ]}
      image={{
        light: "/app-light.png",
        dark: "/app-dark.png",
        alt: "UI Components Preview",
      }}
    />
    </>
  )
}